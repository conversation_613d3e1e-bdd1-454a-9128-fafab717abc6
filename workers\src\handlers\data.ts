
import { Context } from 'hono';
import { EastmoneyApiService } from '../services/eastmoneyApi';
import { CacheService } from '../services/cache';
import { ApiResponse, Env, ProcessedFlowData } from '../types/api';
import { validateStockCode, sanitizeStockCode } from '../services/validator';
import { createLogger } from '../utils/logger';

/**
 * 数据获取API处理器
 */
export class DataHandler {
  private apiService: EastmoneyApiService;
  private cache: CacheService | null;
  private logger: ReturnType<typeof createLogger>;
  private cacheAvailable: boolean;

  constructor(env: Env) {
    this.logger = createLogger('DataHandler', env);
    this.apiService = new EastmoneyApiService();
    this.cacheAvailable = false;
    this.cache = null;

    // 安全初始化缓存服务
    try {
      if (env.STOCK_CACHE) {
        this.cache = new CacheService(env.STOCK_CACHE);
        this.cacheAvailable = true;
        this.logger.info('Cache service initialized successfully');
      } else {
        this.logger.warn('STOCK_CACHE KV namespace not available');
      }
    } catch (error) {
      this.logger.error('Failed to initialize cache service', {
        error: error instanceof Error ? error.message : 'Unknown error',
        hasStockCache: !!env.STOCK_CACHE,
        envKeys: Object.keys(env || {})
      });
      // 继续运行，但缓存不可用
      this.cache = null;
      this.cacheAvailable = false;
    }
  }

  /**
   * 获取单个股票数据
   * GET /api/data/:code
   */
  async getStockData(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      const limit = parseInt(c.req.query('limit') || '240');
      const useCache = c.req.query('cache') !== 'false';

      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (limit < 1 || limit > 1000) {
        return c.json({
          success: false,
          message: '数据条数限制在1-1000之间',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cacheKey = CacheService.getStockDataKey(cleanCode);

      // 尝试从缓存获取数据
      if (useCache && this.cache && this.cacheAvailable) {
        try {
          const cachedData = await this.cache.get<ApiResponse<ProcessedFlowData>>(cacheKey);
          if (cachedData) {
            return c.json({
              ...cachedData,
              fromCache: true,
            });
          }
        } catch (error) {
          this.logger.warn('Cache get failed, continuing without cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // 从API获取数据
      const result = await this.apiService.getStockFlowData(cleanCode, limit);

      // 缓存成功的结果
      if (result.success && this.cache && this.cacheAvailable) {
        try {
          await this.cache.set(cacheKey, result, 60); // 1分钟缓存
          await this.cache.set(CacheService.getLastUpdateKey(cleanCode), new Date().toISOString(), 300); // 5分钟缓存
        } catch (error) {
          this.logger.warn('Cache set failed, continuing without cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return c.json(result, result.success ? 200 : 500);
    } catch (error) {
      console.error('获取股票数据失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 批量获取股票数据
   * GET /api/data/batch
   */
  async getBatchStockData(c: Context): Promise<Response> {
    try {
      const codesParam = c.req.query('codes');
      const limit = parseInt(c.req.query('limit') || '240');
      const useCache = c.req.query('cache') !== 'false';

      if (!codesParam) {
        return c.json({
          success: false,
          message: '股票代码列表不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const codes = codesParam.split(',').map(code => sanitizeStockCode(code.trim())).filter(Boolean);

      if (codes.length === 0) {
        return c.json({
          success: false,
          message: '有效的股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (codes.length > 20) {
        return c.json({
          success: false,
          message: '一次最多只能查询20个股票',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 验证所有股票代码
      const invalidCodes = codes.filter(code => !validateStockCode(code));
      if (invalidCodes.length > 0) {
        return c.json({
          success: false,
          message: `无效的股票代码: ${invalidCodes.join(', ')}`,
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};
      const fromCache: string[] = [];

      // 并发获取数据
      await Promise.all(codes.map(async (code) => {
        try {
          const cacheKey = CacheService.getStockDataKey(code);
          
          // 尝试从缓存获取
          if (useCache && this.cache && this.cacheAvailable) {
            try {
              const cachedData = await this.cache.get<ApiResponse<ProcessedFlowData>>(cacheKey);
              if (cachedData && cachedData.success) {
                results[code] = cachedData.data;
                fromCache.push(code);
                return;
              }
            } catch (error) {
              this.logger.warn('Batch cache get failed', {
                code,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          // 从API获取
          const result = await this.apiService.getStockFlowData(code, limit);

          if (result.success) {
            results[code] = result.data;
            // 缓存结果
            if (this.cache && this.cacheAvailable) {
              try {
                await this.cache.set(cacheKey, result, 60);
                await this.cache.set(CacheService.getLastUpdateKey(code), new Date().toISOString(), 300);
              } catch (error) {
                this.logger.warn('Batch cache set failed', {
                  code,
                  error: error instanceof Error ? error.message : 'Unknown error'
                });
              }
            }
          } else {
            errors[code] = result.message || '获取失败';
          }
        } catch (error) {
          errors[code] = error instanceof Error ? error.message : '未知错误';
        }
      }));

      const successCount = Object.keys(results).length;
      const errorCount = Object.keys(errors).length;

      return c.json({
        success: successCount > 0,
        data: {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: errorCount,
            fromCache: fromCache.length,
          },
        },
        message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
        timestamp: new Date().toISOString(),
      } as ApiResponse, successCount > 0 ? 200 : 500);
    } catch (error) {
      console.error('批量获取股票数据失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取股票最后更新时间
   * GET /api/data/:code/last-update
   */
  async getLastUpdate(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      let lastUpdate: string | null = null;

      if (this.cache && this.cacheAvailable) {
        try {
          lastUpdate = await this.cache.get<string>(CacheService.getLastUpdateKey(cleanCode));
        } catch (error) {
          this.logger.warn('Failed to get last update from cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return c.json({
        success: true,
        data: {
          code: cleanCode,
          lastUpdate: lastUpdate || null,
          hasData: lastUpdate !== null,
          cacheAvailable: this.cacheAvailable,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('获取最后更新时间失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 清除股票数据缓存
   * DELETE /api/data/:code/cache
   */
  async clearCache(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 清除相关缓存
      if (this.cache && this.cacheAvailable) {
        try {
          await Promise.all([
            this.cache.delete(CacheService.getStockDataKey(cleanCode)),
            this.cache.delete(CacheService.getLastUpdateKey(cleanCode)),
          ]);
        } catch (error) {
          this.logger.warn('Failed to clear cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          return c.json({
            success: false,
            message: '缓存清除失败',
            timestamp: new Date().toISOString(),
          } as ApiResponse, 500);
        }
      }

      return c.json({
        success: true,
        data: {
          code: cleanCode,
          cacheAvailable: this.cacheAvailable,
          cleared: this.cacheAvailable
        },
        message: this.cacheAvailable ? '缓存清除成功' : '缓存不可用，无需清除',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('清除缓存失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取API服务状态
   * GET /api/data/status
   */
  async getServiceStatus(c: Context): Promise<Response> {
    try {
      // 获取基础 API 服务状态
      const apiStatus = this.apiService.getServiceStatus();

      // 构建完整状态信息
      const statusData: any = {
        ...apiStatus,
        cacheAvailable: this.cacheAvailable,
        environment: c.env?.ENVIRONMENT || 'unknown',
        timestamp: new Date().toISOString(),
      };

      // 如果缓存不可用，添加警告信息
      if (!this.cacheAvailable) {
        this.logger.warn('Cache service not available in status check');
        statusData.warnings = ['Cache service is not available'];
      }

      return c.json({
        success: true,
        data: statusData,
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      // 增强错误日志
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error('获取服务状态失败', {
        error: errorMessage,
        stack: errorStack,
        env: Object.keys(c.env || {}),
        cacheAvailable: this.cacheAvailable
      });

      console.error('获取服务状态失败:', error);
      console.error('Error details:', {
        message: errorMessage,
        stack: errorStack,
        env: Object.keys(c.env || {}),
        cacheAvailable: this.cacheAvailable
      });

      return c.json({
        success: false,
        message: '服务器内部错误',
        error: errorMessage,
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }
}

