import{r as e,b as s,R as t}from"./vendor-CykFposD.js";import{Q as a,u as r,a as l,b as n,c as i}from"./query-8LrST8fK.js";import{E as c}from"./charts-Bc24t7GR.js";import{A as o,P as d,T as m,a as x,b as h,S as u,C as g,X as y,B as f,M as p,c as b,d as j,D as N,e as v,f as w,R as S,g as k,h as C,i as L,j as T,k as E,l as I,m as A,n as $,o as O,W as D,p as R,q as _,r as z,L as F}from"./ui-BjQUc9EE.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver(e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)}).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}();var P={exports:{}},M={},B=e,W=Symbol.for("react.element"),K=Symbol.for("react.fragment"),U=Object.prototype.hasOwnProperty,V=B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,H={key:!0,ref:!0,__self:!0,__source:!0};function Q(e,s,t){var a,r={},l=null,n=null;for(a in void 0!==t&&(l=""+t),void 0!==s.key&&(l=""+s.key),void 0!==s.ref&&(n=s.ref),s)U.call(s,a)&&!H.hasOwnProperty(a)&&(r[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===r[a]&&(r[a]=s[a]);return{$$typeof:W,type:e,key:l,ref:n,props:r,_owner:V.current}}M.Fragment=K,M.jsx=Q,M.jsxs=Q,P.exports=M;var q=P.exports,J={},Z=s;J.createRoot=Z.createRoot,J.hydrateRoot=Z.hydrateRoot;const G=new a({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:(e,s)=>{if(s instanceof Error&&"status"in s){const e=s.status;if(e>=400&&e<500)return!1}return e<2},retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,retryDelay:1e3}}}),X={STOCKS:["stocks"],STOCK_LIST:["stocks","list"],STOCK_DATA:e=>["stockData",e],STOCK_DATA_BATCH:e=>["stockData","batch",e.sort().join(",")],STOCK_LAST_UPDATE:e=>["stockData",e,"lastUpdate"],API_STATUS:["api","status"]},Y={STOCK_LIST:{staleTime:18e5,cacheTime:36e5},STOCK_DATA:{staleTime:6e4,cacheTime:3e5,refetchInterval:6e4,refetchIntervalInBackground:!1},API_STATUS:{staleTime:3e4,cacheTime:12e4,refetchInterval:3e4}};function ee(e){const s=e.trim();if(!s)return{isValid:!1,message:"股票代码不能为空"};if(6!==s.length)return{isValid:!1,message:"股票代码必须为6位数字"};if(!/^\d{6}$/.test(s))return{isValid:!1,message:"股票代码只能包含数字"};const t=parseInt(s);return t>=6e5&&t<=699999||t>=0&&t<=99999||t>=2e3&&t<=4999||t>=3e5&&t<=399999||t>=688e3&&t<=689999||t>=43e4&&t<=899999?{isValid:!0}:{isValid:!1,message:"请输入有效的股票代码"}}function se(e){return e.trim().padStart(6,"0")}const te={"000001":"平安银行","000002":"万科A","000858":"五粮液",600036:"招商银行",600121:"郑州煤电",600519:"贵州茅台","002415":"海康威视",600276:"恒瑞医药","000725":"京东方A",600793:"宜宾纸业",603067:"振华股份"};const ae="gupiao-stock-list";function re(){const[s,t]=e.useState([]),[a,r]=e.useState(!1),[l,n]=e.useState(null);e.useEffect(()=>{try{const e=localStorage.getItem(ae);if(e){const s=JSON.parse(e);t(s)}}catch(e){n("加载股票列表失败")}},[]);const i=e.useCallback(e=>{try{localStorage.setItem(ae,JSON.stringify(e))}catch(s){n("保存股票列表失败")}},[]),c=e.useCallback(async e=>{r(!0),n(null);try{const a=ee(e);if(!a.isValid)return{success:!1,message:a.message};const r=se(e);if(function(e,s){const t=se(e);return s.includes(t)}(r,s.map(e=>e.code)))return{success:!1,message:"股票代码已存在"};const l=function(e){return te[e]||`股票${e}`}(r),n={code:r,name:l},c=[...s,n];return t(c),i(c),{success:!0,message:"股票添加成功"}}catch(a){const e="添加股票失败";return n(e),{success:!1,message:e}}finally{r(!1)}},[s,i]),o=e.useCallback(e=>{n(null);try{const a=s.filter(s=>s.code!==e);t(a),i(a)}catch(a){n("删除股票失败")}},[s,i]),d=e.useCallback(()=>{n(null);try{t([]),i([])}catch(e){n("清空股票列表失败")}},[i]);return{stocks:s,addStock:c,removeStock:o,clearAllStocks:d,isLoading:a,error:l}}function le({onAddStock:s,isLoading:t=!1}){const[a,r]=e.useState(""),[l,n]=e.useState(null),[i,c]=e.useState(!1),m=e.useCallback(function(e,s){let t;return(...a)=>{clearTimeout(t),t=window.setTimeout(()=>e(...a),s)}}(e=>{if(!e.trim())return n(null),void c(!1);const s=ee(e);n(s.isValid?null:s.message||"无效的股票代码"),c(!1)},300),[]),x=async()=>{if(!a.trim()||l||t)return;const e=await s(a);e.success?(r(""),n(null)):n(e.message||"添加失败")},h=null!==l,u=a.trim()&&!h&&!i&&!t;return q.jsxs("div",{className:"space-y-3",children:[q.jsxs("div",{className:"flex gap-2",children:[q.jsxs("div",{className:"flex-1",children:[q.jsx("input",{type:"text",value:a,onChange:e=>{const s=e.target.value.replace(/\D/g,"").slice(0,6);r(s),s.trim()?(c(!0),m(s)):(n(null),c(!1))},onKeyPress:e=>{"Enter"===e.key&&x()},placeholder:"输入6位股票代码",className:"input "+(h?"border-danger-500 focus:ring-danger-500":""),disabled:t,maxLength:6}),i&&q.jsx("div",{className:"mt-1 text-sm text-gray-500",children:"验证中..."}),h&&q.jsxs("div",{className:"mt-1 flex items-center gap-1 text-sm text-danger-600",children:[q.jsx(o,{className:"w-4 h-4"}),q.jsx("span",{children:l})]})]}),q.jsxs("button",{onClick:x,disabled:!u,className:`btn ${u?"btn-primary":"btn-secondary"} flex items-center gap-2 px-4 py-2`,children:[t?q.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):q.jsx(d,{className:"w-4 h-4"}),"添加"]})]}),q.jsx("div",{className:"text-xs text-gray-500",children:"支持的股票代码：沪市(600xxx)、深市(000xxx)、创业板(300xxx)、科创板(688xxx)等"})]})}function ne({stocks:e,onRemoveStock:s,onSelectStock:t,selectedStock:a}){return 0===e.length?q.jsxs("div",{className:"text-center py-8",children:[q.jsx(m,{className:"w-12 h-12 text-gray-300 mx-auto mb-3"}),q.jsx("p",{className:"text-gray-500 mb-2",children:"暂无股票代码"}),q.jsx("p",{className:"text-sm text-gray-400",children:"请添加股票代码开始监控"})]}):q.jsxs("div",{className:"space-y-2",children:[q.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200",children:[q.jsxs("span",{children:["股票代码 (",e.length,")"]}),q.jsx("span",{children:"操作"})]}),q.jsx("div",{className:"space-y-1 max-h-64 overflow-y-auto",children:e.map(e=>q.jsx(ie,{stock:e,isSelected:a===e.code,onSelect:t,onRemove:s},e.code))}),e.length>1&&q.jsx("div",{className:"pt-3 border-t border-gray-200",children:q.jsxs("button",{onClick:()=>{window.confirm("确定要清空所有股票代码吗？")&&e.forEach(e=>s(e.code))},className:"text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1",children:[q.jsx(x,{className:"w-4 h-4"}),"清空所有"]})})]})}function ie({stock:e,isSelected:s,onSelect:t,onRemove:a}){return q.jsxs("div",{className:`\n        flex items-center justify-between p-3 rounded-lg border transition-all duration-200\n        ${s?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}\n        ${t?"cursor-pointer":""}\n      `,onClick:()=>{t&&t(e.code)},children:[q.jsxs("div",{className:"flex items-center gap-3",children:[q.jsxs("div",{className:"flex flex-col",children:[q.jsx("span",{className:"text-sm font-medium "+(s?"text-primary-700":"text-gray-900"),children:e.name}),q.jsx("span",{className:"text-xs text-gray-500 font-mono",children:e.code})]}),s&&q.jsx("div",{className:"w-2 h-2 bg-primary-500 rounded-full"})]}),q.jsx("button",{onClick:s=>{s.stopPropagation(),window.confirm(`确定要删除股票 ${e.code} 吗？`)&&a(e.code)},className:"p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200",title:"删除股票",children:q.jsx(h,{className:"w-4 h-4"})})]})}function ce({onSelectStock:s,selectedStock:t}){const{stocks:a,addStock:r,removeStock:l,isLoading:n,error:i}=re(),[c,o]=e.useState(null),d=(e,s)=>{o({type:e,message:s}),setTimeout(()=>o(null),3e3)};return q.jsxs("div",{className:"card p-6 h-full",children:[q.jsxs("div",{className:"flex items-center justify-between mb-6",children:[q.jsxs("div",{children:[q.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"股票管理"}),q.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"添加和管理要监控的股票代码"})]}),q.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",children:q.jsx(u,{className:"w-5 h-5"})})]}),c&&q.jsxs("div",{className:`\n          flex items-center gap-2 p-3 rounded-lg mb-4 animate-slide-up\n          ${"success"===c.type?"bg-success-50 text-success-700 border border-success-200":"bg-danger-50 text-danger-700 border border-danger-200"}\n        `,children:["success"===c.type?q.jsx(g,{className:"w-4 h-4"}):q.jsx(y,{className:"w-4 h-4"}),q.jsx("span",{className:"text-sm",children:c.message})]}),i&&q.jsxs("div",{className:"flex items-center gap-2 p-3 rounded-lg mb-4 bg-danger-50 text-danger-700 border border-danger-200",children:[q.jsx(y,{className:"w-4 h-4"}),q.jsx("span",{className:"text-sm",children:i})]}),q.jsx("div",{className:"mb-6",children:q.jsx(le,{onAddStock:async e=>{const s=await r(e);return s.success?d("success",s.message||"股票添加成功"):d("error",s.message||"添加失败"),s},isLoading:n})}),q.jsx("div",{className:"flex-1",children:q.jsx(ne,{stocks:a,onRemoveStock:e=>{l(e),d("success","股票删除成功"),t===e&&s&&s("")},onSelectStock:s,selectedStock:t})}),a.length>0&&q.jsx("div",{className:"mt-6 pt-4 border-t border-gray-200",children:q.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[q.jsxs("span",{children:["已添加 ",a.length," 只股票"]}),q.jsx("span",{children:t?`当前选中: ${t}`:"请选择股票查看数据"})]})})]})}function oe(e,s="wan",t=2){if(0===e)return"0";let a=1,r="";switch(s){case"yuan":a=1,r="元";break;case"wan":a=1e4,r="万";break;case"yi":a=1e8,r="亿"}return`${(e/a).toFixed(t)}${r}`}function de(e,s=2){const t=Math.abs(e);return t>=1e8?oe(e,"yi",s):t>=1e4?oe(e,"wan",s):oe(e,"yuan",0)}function me(e,s="time"){const t=new Date(e);if(isNaN(t.getTime()))return e;switch(s){case"time":return t.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"});case"date":return t.toLocaleDateString("zh-CN");case"datetime":return t.toLocaleString("zh-CN");default:return e}}function xe(e){return e>0?"#ef4444":e<0?"#22c55e":"#6b7280"}function he(e,s=2){return e.toLocaleString("zh-CN",{minimumFractionDigits:s,maximumFractionDigits:s})}function ue(e){const s=new Date,t=new Date(e),a=s.getTime()-t.getTime(),r=Math.floor(a/6e4);if(r<1)return"刚刚";if(r<60)return`${r}分钟前`;if(r<1440){return`${Math.floor(r/60)}小时前`}return`${Math.floor(r/1440)}天前`}const ge="#FF6B9D",ye="#C44569",fe="#F8B500",pe="#6C7CE0",be="#A0E7E5",je={backgroundColor:"#ffffff",textColor:"#333333",axisLineColor:"#e5e7eb",splitLineColor:"#f3f4f6",tooltipBackgroundColor:"rgba(255, 255, 255, 0.95)",tooltipBorderColor:"#e5e7eb"},Ne={backgroundColor:"#1f2937",textColor:"#f9fafb",axisLineColor:"#374151",splitLineColor:"#4b5563",tooltipBackgroundColor:"rgba(31, 41, 55, 0.95)",tooltipBorderColor:"#6b7280"};function ve(e=!1){const s=e?Ne:je;return{backgroundColor:s.backgroundColor,textStyle:{color:s.textColor,fontFamily:"system-ui, -apple-system, sans-serif"},grid:{left:"8%",right:"5%",bottom:"15%",top:"15%",containLabel:!0},tooltip:{backgroundColor:s.tooltipBackgroundColor,borderColor:s.tooltipBorderColor,borderWidth:1,textStyle:{color:s.textColor},trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}}},legend:{textStyle:{color:s.textColor}}}}function we(e){const s=e<768;return{grid:{left:s?"5%":"3%",right:s?"5%":"4%",bottom:s?"15%":"3%",containLabel:!0},legend:{orient:"horizontal",top:s?"bottom":30,itemWidth:s?15:25,itemHeight:s?10:14,textStyle:{fontSize:s?10:12}},xAxis:{axisLabel:{fontSize:s?10:12,rotate:s?45:0}},yAxis:{axisLabel:{fontSize:s?10:12}},dataZoom:s?[]:[{type:"inside",start:70,end:100},{type:"slider",start:70,end:100,height:20,bottom:10}]}}const Se=({klines:s=[],summary:t=null,type:a="line",isDark:r=!1,height:l=400,showToolbar:n=!0,loading:i=!1,error:o=null,className:d=""})=>{const x=e.useRef(null),h=e.useRef(null),[u,g]=e.useState(a),[y,j]=e.useState(!1),[N,v]=e.useState(800);e.useEffect(()=>{const e=()=>{h.current&&v(h.current.offsetWidth)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const w=e.useMemo(()=>{if("line"===u&&s.length>0){const e=function(e,s=!1){const t=ve(s),a=s?Ne:je,r=e.map(e=>me(e.time)),l=[{name:"主力净流入",type:"line",data:e.map(e=>e.mainNetInflow),color:ge,smooth:!0,symbol:"circle",symbolSize:4,showSymbol:!1,lineStyle:{width:3,type:"solid"},emphasis:{lineStyle:{width:4},focus:"series"},areaStyle:{opacity:.1}},{name:"超大单净流入",type:"line",data:e.map(e=>e.superLargeNetInflow),color:ye,smooth:!0,symbol:"circle",symbolSize:4,showSymbol:!1,lineStyle:{width:2.5,type:"solid"},emphasis:{lineStyle:{width:3.5},focus:"series"},areaStyle:{opacity:.08}},{name:"大单净流入",type:"line",data:e.map(e=>e.largeNetInflow),color:fe,smooth:!0,symbol:"circle",symbolSize:4,showSymbol:!1,lineStyle:{width:2.5,type:"solid"},emphasis:{lineStyle:{width:3.5},focus:"series"},areaStyle:{opacity:.08}},{name:"中单净流入",type:"line",data:e.map(e=>e.mediumNetInflow),color:pe,smooth:!0,symbol:"circle",symbolSize:4,showSymbol:!1,lineStyle:{width:2,type:"solid"},emphasis:{lineStyle:{width:3},focus:"series"},areaStyle:{opacity:.06}},{name:"小单净流入",type:"line",data:e.map(e=>e.smallNetInflow),color:be,smooth:!0,symbol:"circle",symbolSize:4,showSymbol:!1,lineStyle:{width:2,type:"solid"},emphasis:{lineStyle:{width:3},focus:"series"},areaStyle:{opacity:.06}}];return{...t,title:{text:"资金流向趋势图",textStyle:{color:a.textColor,fontSize:16,fontWeight:"bold"},left:"center"},tooltip:{...t.tooltip,trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:a.textColor}},formatter:e=>{var s;if(!Array.isArray(e))return"";let t=`<div style="margin-bottom: 4px; font-weight: bold;">${(null==(s=e[0])?void 0:s.axisValue)||""}</div>`;return e.forEach(e=>{const s=de(e.value),a=e.color;t+=`\n            <div style="display: flex; align-items: center; margin: 2px 0;">\n              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${a}; border-radius: 50%; margin-right: 8px;"></span>\n              <span style="flex: 1;">${e.seriesName}:</span>\n              <span style="font-weight: bold; color: ${xe(e.value)};">${s}</span>\n            </div>\n          `}),t}},legend:{...t.legend,top:30,data:l.map(e=>e.name),orient:"horizontal",left:"center",itemWidth:25,itemHeight:14,itemGap:20,textStyle:{fontSize:12,color:a.textColor},icon:"line",lineStyle:{width:3}},xAxis:{type:"category",data:r,axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,rotate:45},splitLine:{show:!1}},yAxis:{type:"value",axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,formatter:e=>de(e)},splitLine:{lineStyle:{color:a.splitLineColor}}},series:l,dataZoom:[{type:"inside",start:70,end:100},{type:"slider",start:70,end:100,height:20,bottom:10}]}}(s,r);return{...e,...we(N)}}if("bar"===u&&t){const e=function(e,s=!1){const t=ve(s),a=s?Ne:je,r=[e.mainNetInflow,e.superLargeNetInflow,e.largeNetInflow,e.mediumNetInflow,e.smallNetInflow],l=[ge,ye,fe,pe,be];return{...t,title:{text:"实时资金流向",textStyle:{color:a.textColor,fontSize:16,fontWeight:"bold"},left:"center"},tooltip:{...t.tooltip,trigger:"axis",formatter:e=>{if(!Array.isArray(e)||0===e.length)return"";const s=e[0],t=de(s.value),a=s.value>=0?"流入":"流出",r=xe(s.value);return`\n          <div style="font-weight: bold; margin-bottom: 4px;">${s.name}净${a}</div>\n          <div style="color: ${r}; font-size: 14px; font-weight: bold;">${t}</div>\n        `}},xAxis:{type:"category",data:["主力","超大单","大单","中单","小单"],axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor}},yAxis:{type:"value",axisLine:{lineStyle:{color:a.axisLineColor}},axisLabel:{color:a.textColor,formatter:e=>de(e)},splitLine:{lineStyle:{color:a.splitLineColor}}},series:[{type:"bar",data:r.map((e,s)=>({value:e,itemStyle:{color:l[s]}})),barWidth:"60%",label:{show:!0,position:"top",formatter:e=>de(e.value),color:a.textColor}}]}}(t,r);return{...e,...we(N)}}return{}},[u,s,t,r,N]),S=e=>{g(e)};return i?q.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"}),q.jsx("p",{className:"text-gray-600",children:"加载图表数据中..."})]})}):o?q.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-red-500 mb-2",children:q.jsx(f,{className:"w-8 h-8 mx-auto"})}),q.jsx("p",{className:"text-red-600 font-medium",children:"图表加载失败"}),q.jsx("p",{className:"text-gray-500 text-sm mt-1",children:o})]})}):0!==s.length||t?q.jsxs("div",{ref:h,className:`bg-white rounded-lg border ${y?"fixed inset-0 z-50":""} ${d}`,children:[n&&q.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"资金流向图表"}),t&&q.jsxs("span",{className:"text-sm text-gray-500",children:[t.name," (",t.code,")"]})]}),q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[q.jsxs("button",{onClick:()=>S("line"),className:"px-3 py-1 rounded text-sm transition-colors "+("line"===u?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"),disabled:0===s.length,children:[q.jsx(m,{className:"w-4 h-4 inline mr-1"}),"趋势图"]}),q.jsxs("button",{onClick:()=>S("bar"),className:"px-3 py-1 rounded text-sm transition-colors "+("bar"===u?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"),disabled:!t,children:[q.jsx(f,{className:"w-4 h-4 inline mr-1"}),"柱状图"]})]}),q.jsx("button",{onClick:()=>{if(x.current){x.current.getEchartsInstance().resize()}},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:"刷新图表",children:q.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),q.jsx("button",{onClick:()=>{if(x.current){const e=x.current.getEchartsInstance().getDataURL({type:"png",backgroundColor:r?"#1f2937":"#ffffff"}),s=document.createElement("a");s.download=`资金流向图表_${(new Date).toISOString().slice(0,10)}.png`,s.href=e,s.click()}},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:"导出图表",children:q.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),q.jsx("button",{onClick:()=>{j(!y)},className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded",title:y?"退出全屏":"全屏显示",children:y?q.jsx(p,{className:"w-4 h-4"}):q.jsx(b,{className:"w-4 h-4"})})]})]}),q.jsx("div",{className:"p-4",children:q.jsx(c,{ref:x,option:w,style:{height:y?"calc(100vh - 120px)":l,width:"100%"},notMerge:!0,lazyUpdate:!0,theme:r?"dark":void 0})})]}):q.jsx("div",{className:`flex items-center justify-center bg-white rounded-lg border ${d}`,style:{height:l},children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-gray-400 mb-2",children:q.jsx(f,{className:"w-8 h-8 mx-auto"})}),q.jsx("p",{className:"text-gray-600",children:"暂无图表数据"}),q.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"请选择股票查看资金流向"})]})})},ke=({klines:s=[],summary:t=null,pageSize:a=20,showSearch:r=!0,showExport:l=!0,className:n=""})=>{const[i,c]=e.useState(1),[o,d]=e.useState("time"),[m,x]=e.useState("desc"),[h,u]=e.useState(""),g=[{key:"time",label:"时间",sortable:!0,width:"120px"},{key:"mainNetInflow",label:"主力净流入",sortable:!0,width:"120px"},{key:"superLargeNetInflow",label:"超大单净流入",sortable:!0,width:"130px"},{key:"largeNetInflow",label:"大单净流入",sortable:!0,width:"120px"},{key:"mediumNetInflow",label:"中单净流入",sortable:!0,width:"120px"},{key:"smallNetInflow",label:"小单净流入",sortable:!0,width:"120px"}],y=e.useMemo(()=>{let e=s;return h&&(e=s.filter(e=>me(e.time,"datetime").includes(h))),e.sort((e,s)=>{let t=e[o],a=s[o];return"time"===o&&(t=new Date(t).getTime(),a=new Date(a).getTime()),"asc"===m?t>a?1:-1:t<a?1:-1}),e},[s,h,o,m]),f=e.useMemo(()=>{const e=(i-1)*a,s=e+a;return y.slice(e,s)},[y,i,a]),p=Math.ceil(y.length/a),b=e=>{const s=de(e),t=xe(e),a=function(e){return e>0?"流入":e<0?"流出":"平"}(e);return q.jsx("span",{style:{color:t},className:"font-medium",children:"平"===a?s:`${a} ${s}`})};return 0===s.length?q.jsxs("div",{className:`bg-white rounded-lg border p-8 text-center ${n}`,children:[q.jsx("div",{className:"text-gray-400 mb-2",children:q.jsx("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:q.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 17v-2m3 2v-4m3 4v-6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),q.jsx("p",{className:"text-gray-600",children:"暂无数据"}),q.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"请选择股票查看详细数据"})]}):q.jsxs("div",{className:`bg-white rounded-lg border ${n}`,children:[q.jsx("div",{className:"p-4 border-b",children:q.jsxs("div",{className:"flex items-center justify-between",children:[q.jsxs("div",{children:[q.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"资金流向明细"}),t&&q.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[t.name," (",t.code,") - 共 ",y.length," 条记录"]})]}),q.jsxs("div",{className:"flex items-center gap-2",children:[r&&q.jsxs("div",{className:"relative",children:[q.jsx(j,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),q.jsx("input",{type:"text",placeholder:"搜索时间...",value:h,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"})]}),l&&q.jsxs("button",{onClick:()=>{const e=[g.map(e=>e.label).join(","),...y.map(e=>g.map(s=>"time"===s.key?me(e[s.key],"datetime"):de(e[s.key])).join(","))].join("\n"),s=new Blob(["\ufeff"+e],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a");t.href=URL.createObjectURL(s),t.download=`资金流向数据_${(new Date).toISOString().slice(0,10)}.csv`,t.click()},className:"flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm",children:[q.jsx(N,{className:"w-4 h-4"}),"导出CSV"]})]})]})}),q.jsx("div",{className:"overflow-x-auto",children:q.jsxs("table",{className:"w-full",children:[q.jsx("thead",{className:"bg-gray-50",children:q.jsx("tr",{children:g.map(e=>{return q.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider "+(e.sortable?"cursor-pointer hover:bg-gray-100":""),style:{width:e.width},onClick:()=>{return e.sortable&&(s=e.key,o===s?x("asc"===m?"desc":"asc"):(d(s),x("desc")),void c(1));var s},children:q.jsxs("div",{className:"flex items-center gap-1",children:[e.label,e.sortable&&(s=e.key,o!==s?q.jsx(v,{className:"w-4 h-4 text-gray-400"}):"asc"===m?q.jsx(v,{className:"w-4 h-4 text-primary-600"}):q.jsx(w,{className:"w-4 h-4 text-primary-600"}))]})},e.key);var s})})}),q.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map((e,s)=>q.jsxs("tr",{className:"hover:bg-gray-50",children:[q.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:me(e.time,"datetime")}),q.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.mainNetInflow)}),q.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.superLargeNetInflow)}),q.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.largeNetInflow)}),q.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.mediumNetInflow)}),q.jsx("td",{className:"px-4 py-3 text-sm",children:b(e.smallNetInflow)})]},`${e.time}-${s}`))})]})}),p>1&&q.jsxs("div",{className:"px-4 py-3 border-t bg-gray-50 flex items-center justify-between",children:[q.jsxs("div",{className:"text-sm text-gray-700",children:["显示第 ",(i-1)*a+1," - ",Math.min(i*a,y.length)," 条， 共 ",y.length," 条记录"]}),q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsx("button",{onClick:()=>c(Math.max(1,i-1)),disabled:1===i,className:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100",children:"上一页"}),q.jsxs("span",{className:"text-sm text-gray-700",children:["第 ",i," / ",p," 页"]}),q.jsx("button",{onClick:()=>c(Math.min(p,i+1)),disabled:i===p,className:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100",children:"下一页"})]})]})]})},Ce=(e,s,t)=>{const a=new Error(e);return a.status=s,a.details=t,a},Le=async(e,s={})=>{const t=`https://gupiao-zijinliu-api-prod.yiukaitlina.workers.dev${e}`,a={headers:{"Content-Type":"application/json",Accept:"application/json"}};try{const e=await fetch(t,{...a,...s});if(!e.ok){const s=await e.text().catch(()=>"Unknown error");throw Ce(`HTTP ${e.status}: ${e.statusText}`,e.status,s)}const r=await e.json();if("object"!=typeof r||!("success"in r))throw Ce("Invalid API response format");return r}catch(r){if(r instanceof TypeError&&r.message.includes("fetch"))throw Ce("Network error: Unable to connect to server");throw r}},Te={async getStocks(){const e=await Le("/api/stocks");if(!e.success)throw Ce(e.message||"Failed to get stocks");return e.data||[]},async addStock(e,s){const t=await Le("/api/stocks",{method:"POST",body:JSON.stringify({code:e,name:s})});if(!t.success)throw Ce(t.message||"Failed to add stock");return t.data},async removeStock(e){const s=await Le(`/api/stocks/${e}`,{method:"DELETE"});if(!s.success)throw Ce(s.message||"Failed to remove stock");return s.data},async addStocksBatch(e){const s=await Le("/api/stocks/batch",{method:"POST",body:JSON.stringify({stocks:e})});if(!s.success)throw Ce(s.message||"Failed to add stocks");return s.data},async clearAllStocks(){const e=await Le("/api/stocks",{method:"DELETE"});if(!e.success)throw Ce(e.message||"Failed to clear stocks");return e.data}},Ee={async getStockData(e,s=240,t=!0){const a=new URLSearchParams({limit:s.toString(),cache:t.toString()}),r=await Le(`/api/data/${e}?${a}`);if(!r.success)throw Ce(r.message||"Failed to get stock data");return r.data},async getBatchStockData(e,s=240,t=!0){const a=new URLSearchParams({codes:e.join(","),limit:s.toString(),cache:t.toString()}),r=await Le(`/api/data/batch?${a}`);if(!r.success)throw Ce(r.message||"Failed to get batch stock data");return r.data},async getLastUpdate(e){const s=await Le(`/api/data/${e}/last-update`);if(!s.success)throw Ce(s.message||"Failed to get last update");return s.data},async clearCache(e){const s=await Le(`/api/data/${e}/cache`,{method:"DELETE"});if(!s.success)throw Ce(s.message||"Failed to clear cache");return s.data},async getServiceStatus(){const e=await Le("/api/data/status");if(!e.success)throw Ce(e.message||"Failed to get service status");return e.data}};function Ie(){const e=r(),s=l(X.STOCK_LIST,Te.getStocks,Y.STOCK_LIST),t=n(({code:e,name:s})=>Te.addStock(e,s),{onSuccess:()=>{e.invalidateQueries(X.STOCK_LIST)}}),a=n(e=>Te.removeStock(e),{onSuccess:(s,t)=>{e.invalidateQueries(X.STOCK_LIST),(e=>{G.removeQueries(X.STOCK_DATA(e)),G.removeQueries(X.STOCK_LAST_UPDATE(e))})(t)}}),i=n(e=>Te.addStocksBatch(e),{onSuccess:()=>{e.invalidateQueries(X.STOCK_LIST)}}),c=n(Te.clearAllStocks,{onSuccess:()=>{e.invalidateQueries(X.STOCK_LIST),e.clear()}}),o=s.data||[];return{...s,data:s.data,stocks:o,isLoading:s.isLoading||t.isLoading||a.isLoading,isError:s.isError,isSuccess:s.isSuccess,isFetching:s.isFetching,isRefetching:s.isRefetching,error:s.error,refetch:s.refetch,remove:s.remove,addStock:async(e,s)=>{await t.mutateAsync({code:e,name:s})},removeStock:async e=>{await a.mutateAsync(e)},addStocksBatch:async e=>{await i.mutateAsync(e)},clearAllStocks:async()=>{await c.mutateAsync()}}}const Ae=({stockCode:s,limit:t=240,className:a=""})=>{const[r,n]=e.useState("chart"),{summary:i,klines:c,isLoading:d,isError:x,error:h,refetch:u,isFetching:g}=function(e,s=240,t={}){const{enabled:a=!0,useCache:r=!0,refetchInterval:n}=t,i=l(X.STOCK_DATA(e),()=>Ee.getStockData(e,s,r),{...Y.STOCK_DATA,enabled:a&&!!e,refetchInterval:n??Y.STOCK_DATA.refetchInterval}),c=i.data,o=(null==c?void 0:c.summary)||null,d=(null==c?void 0:c.klines)||[],m=(null==o?void 0:o.lastUpdate)||null;return{...i,data:i.data,summary:o,klines:d,lastUpdate:m,isLoading:i.isLoading,isError:i.isError,isSuccess:i.isSuccess,isFetching:i.isFetching,isRefetching:i.isRefetching,error:i.error,refetch:i.refetch,remove:i.remove}}(s,t,{enabled:!!s});return d?q.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),q.jsx("p",{className:"text-gray-600 font-medium",children:"正在加载股票数据..."}),q.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["股票代码: ",s]})]})}):x?q.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-red-500 mb-4",children:q.jsx(o,{className:"w-12 h-12 mx-auto"})}),q.jsx("p",{className:"text-red-600 font-medium mb-2",children:"数据加载失败"}),q.jsx("p",{className:"text-gray-500 text-sm mb-4",children:(null==h?void 0:h.message)||"无法获取股票数据，请稍后重试"}),q.jsxs("button",{onClick:()=>u(),className:"btn btn-primary flex items-center gap-2 mx-auto",children:[q.jsx(S,{className:"w-4 h-4"}),"重新加载"]})]})}):i||0!==c.length?q.jsxs("div",{className:`space-y-6 ${a}`,children:[i&&q.jsxs("div",{className:"bg-white rounded-lg border p-6",children:[q.jsxs("div",{className:"flex items-center justify-between mb-4",children:[q.jsxs("div",{children:[q.jsx("h2",{className:"text-xl font-bold text-gray-900",children:i.name}),q.jsxs("p",{className:"text-gray-500",children:[i.code," · 最后更新: ",ue(i.lastUpdate)]})]}),g&&q.jsxs("div",{className:"flex items-center gap-2 text-primary-600",children:[q.jsx(S,{className:"w-4 h-4 animate-spin"}),q.jsx("span",{className:"text-sm",children:"更新中..."})]})]}),q.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[q.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[q.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"主力净流入"}),q.jsx("div",{className:"text-lg font-bold",style:{color:xe(i.mainNetInflow)},children:de(i.mainNetInflow)}),q.jsx("div",{className:"flex items-center justify-center mt-1",children:i.mainNetInflow>=0?q.jsx(m,{className:"w-3 h-3 text-red-500"}):q.jsx(k,{className:"w-3 h-3 text-green-500"})})]}),q.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[q.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"超大单净流入"}),q.jsx("div",{className:"text-lg font-bold",style:{color:xe(i.superLargeNetInflow)},children:de(i.superLargeNetInflow)}),q.jsx("div",{className:"flex items-center justify-center mt-1",children:i.superLargeNetInflow>=0?q.jsx(m,{className:"w-3 h-3 text-red-500"}):q.jsx(k,{className:"w-3 h-3 text-green-500"})})]}),q.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[q.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"大单净流入"}),q.jsx("div",{className:"text-lg font-bold",style:{color:xe(i.largeNetInflow)},children:de(i.largeNetInflow)}),q.jsx("div",{className:"flex items-center justify-center mt-1",children:i.largeNetInflow>=0?q.jsx(m,{className:"w-3 h-3 text-red-500"}):q.jsx(k,{className:"w-3 h-3 text-green-500"})})]}),q.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[q.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"中单净流入"}),q.jsx("div",{className:"text-lg font-bold",style:{color:xe(i.mediumNetInflow)},children:de(i.mediumNetInflow)}),q.jsx("div",{className:"flex items-center justify-center mt-1",children:i.mediumNetInflow>=0?q.jsx(m,{className:"w-3 h-3 text-red-500"}):q.jsx(k,{className:"w-3 h-3 text-green-500"})})]}),q.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[q.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"小单净流入"}),q.jsx("div",{className:"text-lg font-bold",style:{color:xe(i.smallNetInflow)},children:de(i.smallNetInflow)}),q.jsx("div",{className:"flex items-center justify-center mt-1",children:i.smallNetInflow>=0?q.jsx(m,{className:"w-3 h-3 text-red-500"}):q.jsx(k,{className:"w-3 h-3 text-green-500"})})]})]})]}),q.jsxs("div",{className:"bg-white rounded-lg border",children:[q.jsx("div",{className:"border-b",children:q.jsxs("nav",{className:"flex",children:[q.jsxs("button",{onClick:()=>n("chart"),className:"px-6 py-3 text-sm font-medium border-b-2 transition-colors "+("chart"===r?"border-primary-600 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[q.jsx(f,{className:"w-4 h-4 inline mr-2"}),"图表分析"]}),q.jsxs("button",{onClick:()=>n("table"),className:"px-6 py-3 text-sm font-medium border-b-2 transition-colors "+("table"===r?"border-primary-600 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[q.jsx(C,{className:"w-4 h-4 inline mr-2"}),"数据明细"]})]})}),q.jsxs("div",{className:"p-6",children:["chart"===r&&q.jsx(Se,{klines:c,summary:i,type:"line",height:500,showToolbar:!0,loading:g,error:(null==h?void 0:h.message)||null}),"table"===r&&q.jsx(ke,{klines:c,summary:i,pageSize:20,showSearch:!0,showExport:!0})]})]})]}):q.jsx("div",{className:`bg-white rounded-lg border p-8 ${a}`,children:q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-gray-400 mb-4",children:q.jsx(f,{className:"w-12 h-12 mx-auto"})}),q.jsx("p",{className:"text-gray-600 font-medium",children:"暂无数据"}),q.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["股票代码 ",s," 暂无资金流向数据"]})]})})},$e=({selectedStock:s,onStockSelect:t,maxTabs:a=8,className:r=""})=>{const{stocks:l,removeStock:n}=Ie(),[i,c]=e.useState(!1),[o,m]=e.useState(!1),[x,h]=e.useState(!1),u=e.useRef(null),g=()=>{const e=u.current;if(!e)return;const{scrollLeft:s,scrollWidth:t,clientWidth:a}=e;m(s>0),h(s<t-a-1),c(t>a)},y=e=>{const s=u.current;if(!s)return;const t="left"===e?s.scrollLeft-200:s.scrollLeft+200;s.scrollTo({left:t,behavior:"smooth"})};e.useEffect(()=>{const e=u.current;if(!e)return;const s=new ResizeObserver(g);return s.observe(e),e.addEventListener("scroll",g),g(),()=>{s.disconnect(),e.removeEventListener("scroll",g)}},[l]),e.useEffect(()=>{(()=>{if(!s)return;const e=u.current,t=null==e?void 0:e.querySelector(`[data-stock-code="${s}"]`);if(e&&t){const s=e.getBoundingClientRect(),a=t.getBoundingClientRect();(a.left<s.left||a.right>s.right)&&t.scrollIntoView({behavior:"smooth",block:"nearest",inline:"center"})}})()},[s]);const f=l.slice(0,a),p=l.length>a;return 0===l.length?null:q.jsx("div",{className:`bg-white border-b border-gray-200 ${r}`,children:q.jsxs("div",{className:"flex items-center",children:[i&&q.jsx("button",{onClick:()=>y("left"),disabled:!o,className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed","aria-label":"向左滚动",children:q.jsx(L,{className:"w-4 h-4"})}),q.jsx("div",{ref:u,className:"flex-1 flex overflow-x-auto scrollbar-hide",style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:q.jsxs("div",{className:"flex",children:[f.map(e=>{const a=s===e.code;return q.jsx("button",{"data-stock-code":e.code,onClick:()=>t(e.code),className:`\n                    flex-shrink-0 group relative px-4 py-3 text-sm font-medium border-b-2 transition-colors\n                    ${a?"border-primary-600 text-primary-600 bg-primary-50":"border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300"}\n                  `,children:q.jsxs("div",{className:"flex items-center gap-2 max-w-32",children:[q.jsxs("div",{className:"truncate",children:[q.jsx("div",{className:"text-xs text-gray-500 truncate",children:e.name}),q.jsx("div",{className:"font-mono text-xs",children:e.code})]}),q.jsx("button",{onClick:a=>(async(e,a)=>{a.stopPropagation();try{if(await n(e),s===e){const s=l.findIndex(s=>s.code===e),a=l[s+1]||l[s-1];t(a?a.code:"")}}catch(r){}})(e.code,a),className:"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all","aria-label":`移除 ${e.name}`,children:q.jsx(T,{className:"w-3 h-3"})})]})},e.code)}),p&&q.jsx("div",{className:"flex-shrink-0 px-3 py-3 text-xs text-gray-500 border-b-2 border-transparent",children:q.jsxs("div",{className:"flex items-center gap-1",children:[q.jsx(d,{className:"w-3 h-3"}),q.jsxs("span",{children:[l.length-a," 更多"]})]})})]})}),i&&q.jsx("button",{onClick:()=>y("right"),disabled:!x,className:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed","aria-label":"向右滚动",children:q.jsx(E,{className:"w-4 h-4"})})]})})},Oe={enabled:!0,interval:6e4,onlyWhenVisible:!0,stopOnError:!1};function De(s={},t=[]){const a={...Oe,...s},l=r(),[n,i]=e.useState(a.enabled),[c,o]=e.useState(a.interval),[d,m]=e.useState(0),x=e.useRef(null),h=e.useRef(null),u=e.useRef(0),g=e.useCallback(()=>!a.onlyWhenVisible||!document.hidden,[a.onlyWhenVisible]),y=e.useCallback(()=>{if(navigator.onLine&&g())try{t.length>0?t.forEach(e=>{l.invalidateQueries(e)}):l.invalidateQueries(X.STOCKS),u.current=Date.now()}catch(e){a.stopOnError&&i(!1)}},[l,t,g,a.stopOnError]),f=e.useCallback(()=>{h.current&&clearInterval(h.current);const e=Date.now();h.current=window.setInterval(()=>{const s=Date.now()-e,t=Math.max(0,c-s);m(Math.ceil(t/1e3)),t<=0&&(clearInterval(h.current),h.current=null)},1e3)},[c]),p=e.useCallback(()=>{x.current&&clearInterval(x.current),!n||c<=0||(y(),f(),x.current=window.setInterval(()=>{y(),f()},c))},[n,c,y,f]),b=e.useCallback(()=>{x.current&&(clearInterval(x.current),x.current=null),h.current&&(clearInterval(h.current),h.current=null),m(0)},[]),j=e.useCallback(()=>{i(!0)},[]),N=e.useCallback(()=>{i(!1)},[]),v=e.useCallback(e=>{e<1e3||o(e)},[]);return e.useEffect(()=>(n?p():b(),()=>{b()}),[n,p,b]),e.useEffect(()=>{if(!a.onlyWhenVisible)return;const e=()=>{document.hidden?b():n&&p()};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}},[a.onlyWhenVisible,n,p,b]),e.useEffect(()=>{const e=()=>{n&&p()},s=()=>{b()};return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[n,p,b]),e.useEffect(()=>()=>{b()},[b]),{isEnabled:n,interval:c,nextRefreshIn:d,enable:j,disable:N,setInterval:v,refresh:y}}const Re={async getStatus(){const e=await fetch("/api/cron/status"),s=await e.json();if(!s.success)throw new Error(s.message||"Failed to get cron status");return s.data},async triggerTask(){const e=await fetch("/api/cron/trigger",{method:"POST"}),s=await e.json();if(!s.success)throw new Error(s.message||"Failed to trigger task");return s.data}},_e=({className:s=""})=>{const[t,a]=e.useState(!1),i=r(),{data:c,isLoading:o,error:d,refetch:m}=l("cronStatus",Re.getStatus,{refetchInterval:3e4,refetchOnWindowFocus:!1}),x=n(Re.triggerTask,{onSuccess:()=>{i.invalidateQueries("cronStatus"),i.invalidateQueries(["stockData"])}}),h=(null==c?void 0:c.cacheStats)&&c.cacheStats.hits+c.cacheStats.misses>0?(c.cacheStats.hits/(c.cacheStats.hits+c.cacheStats.misses)*100).toFixed(1):"0.0";return o?q.jsx("div",{className:`bg-white rounded-lg border p-4 ${s}`,children:q.jsxs("div",{className:"flex items-center gap-3",children:[q.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"}),q.jsx("span",{className:"text-gray-600",children:"加载定时任务状态..."})]})}):d?q.jsx("div",{className:`bg-white rounded-lg border p-4 ${s}`,children:q.jsxs("div",{className:"flex items-center gap-3 text-red-600",children:[q.jsx(y,{className:"w-5 h-5"}),q.jsx("span",{children:"无法获取定时任务状态"}),q.jsx("button",{onClick:()=>m(),className:"ml-auto p-1 hover:bg-red-50 rounded",title:"重试",children:q.jsx(S,{className:"w-4 h-4"})})]})}):q.jsxs("div",{className:`bg-white rounded-lg border ${s}`,children:[q.jsx("div",{className:"p-4 border-b",children:q.jsxs("div",{className:"flex items-center justify-between",children:[q.jsxs("div",{className:"flex items-center gap-3",children:[q.jsx("div",{className:"p-2 rounded-lg "+((null==c?void 0:c.enabled)?"bg-green-100":"bg-gray-100"),children:q.jsx(I,{className:"w-5 h-5 "+((null==c?void 0:c.enabled)?"text-green-600":"text-gray-500")})}),q.jsxs("div",{children:[q.jsx("h3",{className:"font-semibold text-gray-900",children:"定时任务"}),q.jsxs("p",{className:"text-sm text-gray-500",children:[(null==c?void 0:c.enabled)?"运行中":"已停用"," •",(null==c?void 0:c.lastExecution)?`上次执行: ${ue(c.lastExecution)}`:"未执行过"]})]})]}),q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsxs("button",{onClick:()=>x.mutate(),disabled:x.isLoading||!(null==c?void 0:c.enabled),className:"flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm",title:"手动触发任务",children:[x.isLoading?q.jsx(S,{className:"w-4 h-4 animate-spin"}):q.jsx(A,{className:"w-4 h-4"}),"触发"]}),q.jsx("button",{onClick:()=>a(!t),className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg",title:t?"收起详情":"展开详情",children:q.jsx(S,{className:"w-4 h-4 transition-transform "+(t?"rotate-180":"")})})]})]})}),q.jsx("div",{className:"p-4",children:q.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-lg font-semibold text-gray-900",children:(null==c?void 0:c.config.batchSize)||0}),q.jsx("div",{className:"text-xs text-gray-500",children:"批处理大小"})]}),q.jsxs("div",{className:"text-center",children:[q.jsxs("div",{className:"text-lg font-semibold text-gray-900",children:[(null==c?void 0:c.config.cacheTtl)||0,"s"]}),q.jsx("div",{className:"text-xs text-gray-500",children:"缓存时间"})]}),q.jsxs("div",{className:"text-center",children:[q.jsxs("div",{className:"text-lg font-semibold text-green-600",children:[h,"%"]}),q.jsx("div",{className:"text-xs text-gray-500",children:"缓存命中率"})]}),q.jsxs("div",{className:"text-center",children:[q.jsx("div",{className:"text-lg font-semibold text-gray-900",children:he((null==c?void 0:c.cacheStats.gets)||0,0)}),q.jsx("div",{className:"text-xs text-gray-500",children:"缓存请求"})]})]})}),t&&q.jsx("div",{className:"border-t bg-gray-50 p-4",children:q.jsxs("div",{className:"space-y-4",children:[q.jsxs("div",{children:[q.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"配置信息"}),q.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[q.jsxs("div",{className:"flex justify-between",children:[q.jsx("span",{className:"text-gray-500",children:"最大重试次数:"}),q.jsx("span",{className:"font-medium",children:null==c?void 0:c.config.maxRetries})]}),q.jsxs("div",{className:"flex justify-between",children:[q.jsx("span",{className:"text-gray-500",children:"日志级别:"}),q.jsx("span",{className:"font-medium",children:null==c?void 0:c.config.logLevel})]})]})]}),q.jsxs("div",{children:[q.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"缓存统计"}),q.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[q.jsxs("div",{className:"flex justify-between",children:[q.jsx("span",{className:"text-gray-500",children:"命中:"}),q.jsx("span",{className:"font-medium text-green-600",children:he((null==c?void 0:c.cacheStats.hits)||0,0)})]}),q.jsxs("div",{className:"flex justify-between",children:[q.jsx("span",{className:"text-gray-500",children:"未命中:"}),q.jsx("span",{className:"font-medium text-red-600",children:he((null==c?void 0:c.cacheStats.misses)||0,0)})]}),q.jsxs("div",{className:"flex justify-between",children:[q.jsx("span",{className:"text-gray-500",children:"写入:"}),q.jsx("span",{className:"font-medium text-blue-600",children:he((null==c?void 0:c.cacheStats.sets)||0,0)})]})]})]}),x.data&&q.jsxs("div",{children:[q.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"最近执行结果"}),q.jsxs("div",{className:"bg-white rounded border p-3",children:[q.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[x.data.success?q.jsx(g,{className:"w-4 h-4 text-green-500"}):q.jsx(y,{className:"w-4 h-4 text-red-500"}),q.jsx("span",{className:"text-sm font-medium",children:x.data.success?"执行成功":"执行失败"}),q.jsxs("span",{className:"text-xs text-gray-500",children:["耗时 ",x.data.duration,"ms"]})]}),q.jsxs("div",{className:"text-sm text-gray-600",children:["处理: ",x.data.processed," 个股票， 错误: ",x.data.errors," 个"]})]})]})]})})]})},ze=({showMenuButton:s=!1,onMenuClick:a,isDark:r=!1,onThemeToggle:n,className:i=""})=>{const[c,o]=t.useState(navigator.onLine),[d,m]=e.useState(!1),{isHealthy:x,canMakeRequest:h,isLoading:g}=function(){var e,s;const t=l(X.API_STATUS,Ee.getServiceStatus,Y.API_STATUS),a=t.data,r=(null==a?void 0:a.isHealthy)||!1,n=(null==(e=null==a?void 0:a.rateLimitStatus)?void 0:e.canMakeRequest)||!1,i=(null==(s=null==a?void 0:a.rateLimitStatus)?void 0:s.nextAvailableTime)||0;return{...t,data:t.data,isHealthy:r,canMakeRequest:n,nextAvailableTime:i,isLoading:t.isLoading,isError:t.isError,isSuccess:t.isSuccess,isFetching:t.isFetching,isRefetching:t.isRefetching,error:t.error,refetch:t.refetch,remove:t.remove}}(),y=function(e={}){return De(e,[X.STOCKS])}({enabled:!0,interval:6e4,onlyWhenVisible:!0});return t.useEffect(()=>{const e=()=>o(!0),s=()=>o(!1);return window.addEventListener("online",e),window.addEventListener("offline",s),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}},[]),q.jsxs("header",{className:`bg-white border-b border-gray-200 px-4 py-3 ${i}`,children:[q.jsxs("div",{className:"flex items-center justify-between",children:[q.jsxs("div",{className:"flex items-center gap-4",children:[s&&q.jsx("button",{onClick:a,className:"lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"打开菜单",children:q.jsx($,{className:"w-5 h-5"})}),q.jsxs("div",{className:"flex items-center gap-3",children:[q.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg",children:q.jsx(O,{className:"w-5 h-5 text-white"})}),q.jsxs("div",{children:[q.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"股票资金流向监控"}),q.jsx("p",{className:"text-xs text-gray-500 hidden sm:block",children:"实时监控股票资金流向数据"})]})]})]}),q.jsxs("div",{className:"flex items-center gap-3",children:[q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsxs("div",{className:"flex items-center gap-1",title:c?"网络连接正常":"网络连接断开",children:[c?q.jsx(D,{className:"w-4 h-4 text-green-500"}):q.jsx(R,{className:"w-4 h-4 text-red-500"}),q.jsx("span",{className:"text-xs text-gray-500 hidden sm:inline",children:c?"在线":"离线"})]}),q.jsxs("div",{className:"flex items-center gap-1",title:x?"API服务正常":"API服务异常",children:[g?q.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}):q.jsx("div",{className:"w-2 h-2 rounded-full "+(x?"bg-green-500":"bg-red-500")}),q.jsx("span",{className:"text-xs text-gray-500 hidden sm:inline",children:"API"})]}),y.isEnabled&&q.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[q.jsx(S,{className:"w-3 h-3"}),q.jsxs("span",{className:"hidden sm:inline",children:[y.nextRefreshIn,"s"]})]})]}),q.jsx("div",{className:"w-px h-4 bg-gray-300"}),q.jsxs("div",{className:"flex items-center gap-1",children:[q.jsx("button",{onClick:()=>y.refresh(),disabled:!c||!h,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"手动刷新数据",children:q.jsx(S,{className:"w-4 h-4"})}),q.jsx("button",{onClick:()=>y.isEnabled?y.disable():y.enable(),className:"p-2 rounded-lg transition-colors "+(y.isEnabled?"text-primary-600 bg-primary-50 hover:bg-primary-100":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),title:y.isEnabled?"停止自动刷新":"启动自动刷新",children:q.jsx(S,{className:"w-4 h-4 "+(y.isEnabled?"animate-spin":"")})}),n&&q.jsx("button",{onClick:n,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:r?"切换到亮色主题":"切换到暗色主题",children:r?q.jsx(_,{className:"w-4 h-4"}):q.jsx(z,{className:"w-4 h-4"})}),q.jsxs("div",{className:"relative",children:[q.jsx("button",{onClick:()=>m(!d),className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"定时任务状态",children:q.jsx(I,{className:"w-4 h-4"})}),d&&q.jsxs(q.Fragment,{children:[q.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>m(!1)}),q.jsx("div",{className:"absolute right-0 top-full mt-2 w-96 z-50",children:q.jsx(_e,{})})]})]}),q.jsx("button",{className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors",title:"设置",children:q.jsx(u,{className:"w-4 h-4"})})]})]})]}),!h&&q.jsx("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg",children:q.jsxs("div",{className:"flex items-center gap-2 text-yellow-700 text-sm",children:[q.jsx(S,{className:"w-4 h-4"}),q.jsx("span",{children:"API请求频率限制中，请稍后再试"})]})})]})},Fe=({selectedStock:e,onStockSelect:s,isOpen:t=!0,onClose:a,className:r=""})=>{const{stocks:l,error:n}=Ie();return q.jsxs(q.Fragment,{children:[t&&a&&q.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:a}),q.jsxs("aside",{className:`\n          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto\n          w-80 bg-white border-r border-gray-200\n          transform transition-transform duration-300 ease-in-out\n          ${t?"translate-x-0":"-translate-x-full lg:translate-x-0"}\n          ${r}\n        `,children:[q.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 lg:hidden",children:[q.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"股票管理"}),a&&q.jsx("button",{onClick:a,className:"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"关闭菜单",children:q.jsx(T,{className:"w-5 h-5"})})]}),q.jsxs("div",{className:"flex flex-col h-full lg:h-auto",children:[q.jsx("div",{className:"flex-1 p-4",children:q.jsx(ce,{selectedStock:e,onSelectStock:s})}),q.jsxs("div",{className:"border-t border-gray-200 p-4 bg-gray-50",children:[q.jsxs("div",{className:"flex items-center justify-between text-sm",children:[q.jsxs("div",{className:"flex items-center gap-2 text-gray-600",children:[q.jsx(F,{className:"w-4 h-4"}),q.jsx("span",{children:"监控股票"})]}),q.jsxs("div",{className:"font-medium text-gray-900",children:[l.length," 只"]})]}),e&&q.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["当前选中: ",e]}),n&&q.jsxs("div",{className:"mt-2 text-xs text-red-600",children:["加载失败: ",n.message]})]})]})]})]})},Pe=({selectedStock:e,className:s=""})=>{var t;const{stocks:a,isLoading:r,error:l}=Ie();return q.jsx("main",{className:`flex-1 overflow-auto ${s}`,children:q.jsx("div",{className:"p-4 lg:p-6",children:e?q.jsxs(q.Fragment,{children:[q.jsx("div",{className:"mb-6",children:q.jsxs("nav",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[q.jsx("span",{children:"股票监控"}),q.jsx("span",{children:"/"}),q.jsx("span",{className:"text-gray-900 font-medium",children:(null==(t=a.find(s=>s.code===e))?void 0:t.name)||e}),q.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:e})]})}),q.jsx(Ae,{stockCode:e,limit:240})]}):q.jsx("div",{className:"bg-white rounded-lg border",children:r?q.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[q.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"}),q.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"正在加载股票列表..."}),q.jsx("p",{className:"text-gray-500",children:"请稍候，正在获取您的股票监控列表"})]}):l?q.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[q.jsx("div",{className:"text-red-500 mb-4",children:q.jsx(o,{className:"w-16 h-16 mx-auto"})}),q.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"加载失败"}),q.jsxs("p",{className:"text-gray-500 mb-4",children:["无法加载股票列表: ",l.message]}),q.jsx("button",{onClick:()=>window.location.reload(),className:"btn btn-primary",children:"重新加载"})]}):0===a.length?q.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[q.jsx("div",{className:"text-gray-400 mb-6",children:q.jsx(m,{className:"w-20 h-20 mx-auto"})}),q.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"开始监控股票资金流向"}),q.jsx("p",{className:"text-gray-500 mb-6 max-w-md",children:"您还没有添加任何股票到监控列表。请在左侧添加股票代码，开始监控资金流向数据。"}),q.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),q.jsx("span",{children:"支持沪深两市股票代码"})]}),q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),q.jsx("span",{children:"实时资金流向数据"})]}),q.jsxs("div",{className:"flex items-center gap-2",children:[q.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"}),q.jsx("span",{children:"专业图表分析"})]})]})]}):q.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-center",children:[q.jsx("div",{className:"text-gray-400 mb-6",children:q.jsx(f,{className:"w-20 h-20 mx-auto"})}),q.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-3",children:"选择股票查看数据"}),q.jsxs("p",{className:"text-gray-500 mb-4 max-w-md",children:["您已添加 ",a.length," 只股票到监控列表。请在左侧选择一只股票，查看详细的资金流向数据和图表分析。"]}),q.jsxs("div",{className:"text-sm text-gray-600",children:[q.jsx("p",{children:"监控列表中的股票:"}),q.jsxs("div",{className:"mt-2 flex flex-wrap gap-2 justify-center",children:[a.slice(0,5).map(e=>q.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs",children:[e.name," (",e.code,")"]},e.code)),a.length>5&&q.jsxs("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs",children:["+",a.length-5," 更多"]})]})]})]})})})})},Me=({children:s})=>{const[t,a]=e.useState(null),[r,l]=e.useState(!1),[n,i]=e.useState(!1),[c,o]=e.useState(!1),{stocks:d}=Ie();e.useEffect(()=>{const e=()=>{o(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e.useEffect(()=>{d.length>0&&!t&&a(d[0].code)},[d,t]),e.useEffect(()=>{c||l(!1)},[c]);const m=e=>{a(e),c&&l(!1)};return q.jsxs("div",{className:"min-h-screen bg-gray-50 "+(n?"dark":""),children:[q.jsx(ze,{showMenuButton:c,onMenuClick:()=>l(!0),isDark:n,onThemeToggle:()=>{i(!n),document.documentElement.classList.toggle("dark",!n)}}),q.jsxs("div",{className:"flex h-[calc(100vh-64px)]",children:[q.jsx(Fe,{selectedStock:t,onStockSelect:m,isOpen:!c||r,onClose:c?()=>l(!1):void 0}),q.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[d.length>0&&q.jsx($e,{selectedStock:t,onStockSelect:m,maxTabs:c?3:8}),s||q.jsx(Pe,{selectedStock:t})]})]})]})};function Be(){return q.jsx(Me,{})}J.createRoot(document.getElementById("root")).render(q.jsx(t.StrictMode,{children:q.jsxs(i,{client:G,children:[q.jsx(Be,{}),!1]})}));
